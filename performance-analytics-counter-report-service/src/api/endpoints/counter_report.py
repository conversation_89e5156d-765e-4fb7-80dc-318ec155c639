# Copyright (c) 2021, Syskron GmbH. All rights reserved.

from typing import Optional
from aws_lambda_powertools import Logger, Tracer
from fastapi import APIRouter, HTTPException, Path, Query, Request, status, Depends
from lib_cloud_sdk.util.common import validate_fast_api_timerange
from performance_analytics.fast_api.dependencies.s2a_properties import Share2ActProperties

from api.endpoints.models import CounterReportResponse
from api.endpoints.units_service import get_units_report

LOGGER = Logger()

router = APIRouter()

TRACER = Tracer()


# pylint: disable=too-many-positional-arguments,too-many-arguments
@router.get(
    "/v2/{line_id}/{machine_id}",
    response_model=CounterReportResponse,
    response_model_exclude_none=True,
    dependencies=[Depends(validate_fast_api_timerange)],
)
@TRACER.capture_method(capture_response=False)
def counter_report(
    request: Request,
    properties: Share2ActProperties,
    line_id: str = Path(..., description="Line identifier"),
    machine_id: str = Path(..., description="Machine identifier"),
    time_from: int = Query(..., description="Start timestamp in milliseconds"),
    time_to: Optional[int] = Query(
        None, description="End timestamp in milliseconds (optional, defaults to current time)"
    ),
    line_kpi: int = Query(default=0, description="Use line-level KPIs (1) or machine-level (0)"),
) -> CounterReportResponse:
    """
    Get counter report for a specific machine or line.

    Returns units produced, defective units, and total units for the specified timerange.
    When line_kpi=1, uses block-level counters; otherwise uses machine-level counters.
    """
    try:
        LOGGER.info(
            "Processing counter report request",
            extra={
                "line_id": line_id,
                "machine_id": machine_id,
                "time_from": time_from,
                "time_to": time_to,
                "line_kpi": line_kpi,
                "account": properties.account,
            },
        )

        # Set default time_to to current timestamp if not provided
        if time_to is None:
            import time

            time_to = int(time.time() * 1000)

        # Get the units report using the shared service
        result = get_units_report(
            time_from=time_from,
            time_to=time_to,
            machine_id=machine_id,
            line_id=line_id,
            account=properties.account,
            line_kpi=line_kpi,
        )

        LOGGER.info(
            "Counter report request completed successfully",
            extra={
                "line_id": line_id,
                "machine_id": machine_id,
                "units_produced": result.units_produced,
                "units_defect": result.units_defect,
                "units_total": result.units_total,
            },
        )

        return result

    except HTTPException as http_excep:
        LOGGER.warning(
            "counter_report failed",
            extra={
                "raised_exception": http_excep,
                "event": request.scope.get("aws.event"),
                "line_id": line_id,
                "machine_id": machine_id,
            },
            exc_info=True,
        )
        raise http_excep
    except Exception as excep:
        LOGGER.warning(
            "counter_report failed",
            extra={
                "exception_type": type(excep),
                "raised_exception": excep,
                "event": request.scope.get("aws.event"),
                "line_id": line_id,
                "machine_id": machine_id,
            },
            exc_info=True,
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error occurred while processing counter report",
        ) from excep
